<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON>p Code - Bài 4: <PERSON><PERSON>/<PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" type="image/png" href="../../../../favicon.png">
    
    <style>
        /* Dark Background with Starfield Effect */
        body {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Shooting stars animation */
        .shooting-star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #FFD700;
            border-radius: 50%;
            box-shadow: 0 0 10px #FFD700;
            animation: shoot 3s linear infinite;
        }

        @keyframes shoot {
            0% {
                transform: translateX(-100px) translateY(100px);
                opacity: 1;
            }
            100% {
                transform: translateX(100vw) translateY(-100px);
                opacity: 0;
            }
        }

        /* Floating particles */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 215, 0, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .assignment-container {
            max-width: 1200px;
            width: 100%;
            margin: 120px auto 50px;
            padding: 0 20px;
            position: relative;
            z-index: 1;
            box-sizing: border-box;
        }

        .assignment-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
        }

        .assignment-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #FFD700;
        }

        .problems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .problem-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }

        .problem-card:hover {
            transform: translateY(-5px);
            border-color: #FFD700;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
        }

        .problem-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .problem-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #FFD700;
        }

        .difficulty {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .difficulty.easy {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .difficulty.medium {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
        }

        .difficulty.hard {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .problem-description {
            color: #ccc;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .problem-progress {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            margin-right: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            border-radius: 4px;
            transition: width 0.3s;
            width: 0%;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #FFD700;
            font-weight: bold;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            margin: 2% auto;
            padding: 30px;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 20px;
            top: 15px;
        }

        .close:hover {
            color: #FFD700;
        }

        .modal-header {
            margin-bottom: 20px;
            padding-right: 40px;
        }

        .modal-title {
            color: #FFD700;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .code-editor {
            width: 100%;
            min-height: 300px;
            background: #2d3748;
            color: #e2e8f0;
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            margin-bottom: 20px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: #1a1a2e;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 215, 0, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 215, 0, 0.1);
            border-color: #FFD700;
        }

        .results-section {
            display: none;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .test-progress {
            margin-bottom: 20px;
        }

        .test-progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .test-progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 10px;
            transition: width 0.5s;
            width: 0%;
        }

        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .test-case {
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .test-case.passed {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid #28a745;
        }

        .test-case.failed {
            background: rgba(220, 53, 69, 0.2);
            border: 1px solid #dc3545;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            color: #FFD700;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .back-link:hover {
            text-decoration: underline;
            color: #FFA500;
        }

        .back-link i {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <!-- Shooting stars -->
    <div class="shooting-star" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
    <div class="shooting-star" style="top: 40%; left: 30%; animation-delay: 1s;"></div>
    <div class="shooting-star" style="top: 60%; left: 50%; animation-delay: 2s;"></div>
    <div class="shooting-star" style="top: 80%; left: 70%; animation-delay: 3s;"></div>

    <!-- Floating particles -->
    <div class="particle" style="top: 10%; left: 80%; animation-delay: 0s;"></div>
    <div class="particle" style="top: 30%; left: 60%; animation-delay: 1s;"></div>
    <div class="particle" style="top: 50%; left: 40%; animation-delay: 2s;"></div>
    <div class="particle" style="top: 70%; left: 20%; animation-delay: 3s;"></div>

    <div class="assignment-container">
        <a href="../python-c.html" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - C
        </a>

        <div class="assignment-header">
            <h1>Luyện Tập Code - Bài 4</h1>
            <p>Toán Tử và Nhập/Xuất Dữ Liệu</p>
            <p><strong>14 bài tập</strong> từ dễ đến khó với hệ thống testcase tự động</p>
        </div>

        <div class="problems-grid" id="problemsGrid">
            <!-- Problems will be loaded here -->
        </div>
    </div>

    <!-- Modal for coding practice -->
    <div id="codingModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div class="modal-header">
                <div class="modal-title" id="modalTitle"></div>
                <div id="modalDescription"></div>
                <div id="modalObjective"></div>
            </div>
            
            <textarea id="codeEditor" class="code-editor" placeholder="Nhập code Python của bạn vào đây..."></textarea>
            
            <div class="action-buttons">
                <button class="btn btn-primary" id="runTestsBtn">
                    <i class="fas fa-play"></i> Chạy Testcase
                </button>
                <button class="btn btn-secondary" id="resetCodeBtn">
                    <i class="fas fa-refresh"></i> Reset Code
                </button>
            </div>

            <div class="results-section" id="resultsSection">
                <div class="test-progress">
                    <div class="test-progress-bar">
                        <div class="test-progress-fill" id="testProgressFill"></div>
                    </div>
                    <div id="testProgressText">Sẵn sàng chạy testcase...</div>
                </div>
                <div class="test-results" id="testResults"></div>
            </div>
        </div>
    </div>

    <!-- Embedded coding problems data -->
    <script>
        // 14 coding problems embedded directly
        const codingProblems = [
            {
                id: 1,
                title: "Chào Hỏi Tương Tác",
                difficulty: "Dễ",
                description: "Viết chương trình hỏi tên người dùng, sau đó in ra lời chào kèm theo tên của họ.",
                objective: "Luyện tập input(), biến chuỗi, và nối chuỗi cơ bản.",
                sampleTestcase: { input: ["An"], expectedOutput: "Xin chao ban An, chuc ban mot ngay tot lanh!" },
                testcases: [
                    { input: ["An"], expectedOutput: "Xin chao ban An, chuc ban mot ngay tot lanh!" },
                    { input: ["Minh"], expectedOutput: "Xin chao ban Minh, chuc ban mot ngay tot lanh!" },
                    { input: ["Linh"], expectedOutput: "Xin chao ban Linh, chuc ban mot ngay tot lanh!" },
                    { input: ["Duc"], expectedOutput: "Xin chao ban Duc, chuc ban mot ngay tot lanh!" },
                    { input: ["Hoa"], expectedOutput: "Xin chao ban Hoa, chuc ban mot ngay tot lanh!" },
                    { input: ["Nam"], expectedOutput: "Xin chao ban Nam, chuc ban mot ngay tot lanh!" },
                    { input: ["Trang"], expectedOutput: "Xin chao ban Trang, chuc ban mot ngay tot lanh!" },
                    { input: ["Quan"], expectedOutput: "Xin chao ban Quan, chuc ban mot ngay tot lanh!" },
                    { input: ["Vy"], expectedOutput: "Xin chao ban Vy, chuc ban mot ngay tot lanh!" },
                    { input: ["Khanh"], expectedOutput: "Xin chao ban Khanh, chuc ban mot ngay tot lanh!" }
                ]
            },
            {
                id: 2,
                title: "Máy Tính Cộng Đơn Giản",
                difficulty: "Dễ",
                description: "Viết chương trình nhận vào hai số nguyên từ người dùng, tính tổng của chúng và in kết quả ra màn hình.",
                objective: "Luyện tập input(), ép kiểu int(), và toán tử +.",
                sampleTestcase: { input: ["10", "25"], expectedOutput: "Tong cua hai so la: 35" },
                testcases: [
                    { input: ["10", "25"], expectedOutput: "Tong cua hai so la: 35" },
                    { input: ["5", "7"], expectedOutput: "Tong cua hai so la: 12" },
                    { input: ["0", "0"], expectedOutput: "Tong cua hai so la: 0" },
                    { input: ["100", "200"], expectedOutput: "Tong cua hai so la: 300" },
                    { input: ["15", "30"], expectedOutput: "Tong cua hai so la: 45" },
                    { input: ["8", "12"], expectedOutput: "Tong cua hai so la: 20" },
                    { input: ["50", "75"], expectedOutput: "Tong cua hai so la: 125" },
                    { input: ["3", "9"], expectedOutput: "Tong cua hai so la: 12" },
                    { input: ["20", "40"], expectedOutput: "Tong cua hai so la: 60" },
                    { input: ["1", "1"], expectedOutput: "Tong cua hai so la: 2" }
                ]
            },
            {
                id: 3,
                title: "Diện Tích Hình Vuông",
                difficulty: "Dễ",
                description: "Viết chương trình nhận vào độ dài cạnh của hình vuông, tính và in ra diện tích.",
                objective: "Luyện tập input(), ép kiểu float(), và toán tử **.",
                sampleTestcase: { input: ["5"], expectedOutput: "Dien tich hinh vuong la: 25" },
                testcases: [
                    { input: ["5"], expectedOutput: "Dien tich hinh vuong la: 25" },
                    { input: ["3"], expectedOutput: "Dien tich hinh vuong la: 9" },
                    { input: ["7"], expectedOutput: "Dien tich hinh vuong la: 49" },
                    { input: ["2.5"], expectedOutput: "Dien tich hinh vuong la: 6.25" },
                    { input: ["10"], expectedOutput: "Dien tich hinh vuong la: 100" },
                    { input: ["1"], expectedOutput: "Dien tich hinh vuong la: 1" },
                    { input: ["4"], expectedOutput: "Dien tich hinh vuong la: 16" },
                    { input: ["6"], expectedOutput: "Dien tich hinh vuong la: 36" },
                    { input: ["8"], expectedOutput: "Dien tich hinh vuong la: 64" },
                    { input: ["12"], expectedOutput: "Dien tich hinh vuong la: 144" }
                ]
            },
            {
                id: 4,
                title: "Diện Tích Hình Tròn",
                difficulty: "Dễ",
                description: "Viết chương trình nhận vào bán kính của hình tròn, tính và in ra diện tích (sử dụng π = 3.14159).",
                objective: "Luyện tập với số thực và công thức toán học.",
                sampleTestcase: { input: ["3"], expectedOutput: "Dien tich hinh tron la: 28.27431" },
                testcases: [
                    { input: ["3"], expectedOutput: "Dien tich hinh tron la: 28.27431" },
                    { input: ["5"], expectedOutput: "Dien tich hinh tron la: 78.53975" },
                    { input: ["2"], expectedOutput: "Dien tich hinh tron la: 12.56636" },
                    { input: ["1"], expectedOutput: "Dien tich hinh tron la: 3.14159" },
                    { input: ["4"], expectedOutput: "Dien tich hinh tron la: 50.26544" },
                    { input: ["6"], expectedOutput: "Dien tich hinh tron la: 113.09724" },
                    { input: ["7"], expectedOutput: "Dien tich hinh tron la: 153.93791" },
                    { input: ["10"], expectedOutput: "Dien tich hinh tron la: 314.159" },
                    { input: ["2.5"], expectedOutput: "Dien tich hinh tron la: 19.634937500000002" },
                    { input: ["8"], expectedOutput: "Dien tich hinh tron la: 201.06176" }
                ]
            },
            {
                id: 5,
                title: "Định Dạng Ngày Tháng",
                difficulty: "Dễ",
                description: "Viết chương trình nhận vào ngày, tháng, năm riêng biệt, sau đó in ra dưới dạng ngày/tháng/năm.",
                objective: "Luyện tập nối chuỗi và định dạng dữ liệu.",
                sampleTestcase: { input: ["15", "8", "2024"], expectedOutput: "Ngay thang nam cua ban la: 15/8/2024" },
                testcases: [
                    { input: ["15", "8", "2024"], expectedOutput: "Ngay thang nam cua ban la: 15/8/2024" },
                    { input: ["1", "1", "2025"], expectedOutput: "Ngay thang nam cua ban la: 1/1/2025" },
                    { input: ["25", "12", "2023"], expectedOutput: "Ngay thang nam cua ban la: 25/12/2023" },
                    { input: ["3", "5", "2024"], expectedOutput: "Ngay thang nam cua ban la: 3/5/2024" },
                    { input: ["10", "10", "2024"], expectedOutput: "Ngay thang nam cua ban la: 10/10/2024" },
                    { input: ["7", "2", "2024"], expectedOutput: "Ngay thang nam cua ban la: 7/2/2024" },
                    { input: ["20", "11", "2024"], expectedOutput: "Ngay thang nam cua ban la: 20/11/2024" },
                    { input: ["31", "12", "2024"], expectedOutput: "Ngay thang nam cua ban la: 31/12/2024" },
                    { input: ["14", "2", "2024"], expectedOutput: "Ngay thang nam cua ban la: 14/2/2024" },
                    { input: ["9", "9", "2024"], expectedOutput: "Ngay thang nam cua ban la: 9/9/2024" }
                ]
            },
            {
                id: 6,
                title: "Tính Tổng Tiền Hàng",
                difficulty: "Trung Bình",
                description: "Một cửa hàng bán bút và vở. Viết chương trình nhận vào số lượng bút và số lượng vở khách mua. Biết giá một chiếc bút là 5000 VND, giá một quyển vở là 10000 VND. Tính và in ra tổng số tiền khách phải trả.",
                objective: "Kết hợp nhiều phép tính, biến, input(), int().",
                sampleTestcase: { input: ["3", "5"], expectedOutput: "Tong so tien phai tra la: 65000 VND" },
                testcases: [
                    { input: ["3", "5"], expectedOutput: "Tong so tien phai tra la: 65000 VND" },
                    { input: ["2", "3"], expectedOutput: "Tong so tien phai tra la: 40000 VND" },
                    { input: ["0", "0"], expectedOutput: "Tong so tien phai tra la: 0 VND" },
                    { input: ["1", "1"], expectedOutput: "Tong so tien phai tra la: 15000 VND" },
                    { input: ["10", "5"], expectedOutput: "Tong so tien phai tra la: 100000 VND" },
                    { input: ["5", "0"], expectedOutput: "Tong so tien phai tra la: 25000 VND" },
                    { input: ["0", "8"], expectedOutput: "Tong so tien phai tra la: 80000 VND" },
                    { input: ["7", "2"], expectedOutput: "Tong so tien phai tra la: 55000 VND" },
                    { input: ["4", "6"], expectedOutput: "Tong so tien phai tra la: 80000 VND" },
                    { input: ["12", "10"], expectedOutput: "Tong so tien phai tra la: 160000 VND" }
                ]
            },
            {
                id: 7,
                title: "Đổi Phút Ra Giờ",
                difficulty: "Trung Bình",
                description: "Viết chương trình nhận vào số phút, đổi ra giờ và phút còn lại.",
                objective: "Luyện tập toán tử // và % để chia lấy nguyên và chia lấy dư.",
                sampleTestcase: { input: ["125"], expectedOutput: "125 phut = 2 gio 5 phut" },
                testcases: [
                    { input: ["125"], expectedOutput: "125 phut = 2 gio 5 phut" },
                    { input: ["90"], expectedOutput: "90 phut = 1 gio 30 phut" },
                    { input: ["60"], expectedOutput: "60 phut = 1 gio 0 phut" },
                    { input: ["45"], expectedOutput: "45 phut = 0 gio 45 phut" },
                    { input: ["180"], expectedOutput: "180 phut = 3 gio 0 phut" },
                    { input: ["75"], expectedOutput: "75 phut = 1 gio 15 phut" },
                    { input: ["200"], expectedOutput: "200 phut = 3 gio 20 phut" },
                    { input: ["30"], expectedOutput: "30 phut = 0 gio 30 phut" },
                    { input: ["150"], expectedOutput: "150 phut = 2 gio 30 phut" },
                    { input: ["240"], expectedOutput: "240 phut = 4 gio 0 phut" }
                ]
            },
            {
                id: 8,
                title: "Tính Tuổi",
                difficulty: "Trung Bình",
                description: "Viết chương trình nhận vào năm sinh, tính và in ra tuổi hiện tại (giả sử năm hiện tại là 2024).",
                objective: "Luyện tập phép trừ và làm việc với dữ liệu thời gian.",
                sampleTestcase: { input: ["2000"], expectedOutput: "Tuoi cua ban la: 24" },
                testcases: [
                    { input: ["2000"], expectedOutput: "Tuoi cua ban la: 24" },
                    { input: ["1995"], expectedOutput: "Tuoi cua ban la: 29" },
                    { input: ["2005"], expectedOutput: "Tuoi cua ban la: 19" },
                    { input: ["1990"], expectedOutput: "Tuoi cua ban la: 34" },
                    { input: ["2010"], expectedOutput: "Tuoi cua ban la: 14" },
                    { input: ["1985"], expectedOutput: "Tuoi cua ban la: 39" },
                    { input: ["2001"], expectedOutput: "Tuoi cua ban la: 23" },
                    { input: ["1998"], expectedOutput: "Tuoi cua ban la: 26" },
                    { input: ["2015"], expectedOutput: "Tuoi cua ban la: 9" },
                    { input: ["1980"], expectedOutput: "Tuoi cua ban la: 44" }
                ]
            },
            {
                id: 9,
                title: "Đổi Độ C Sang Độ F",
                difficulty: "Trung Bình",
                description: "Viết chương trình nhận vào nhiệt độ Celsius, đổi sang Fahrenheit theo công thức F = C * 9/5 + 32.",
                objective: "Luyện tập công thức toán học và thứ tự ưu tiên phép toán.",
                sampleTestcase: { input: ["25"], expectedOutput: "25 do C = 77 do F" },
                testcases: [
                    { input: ["25"], expectedOutput: "25 do C = 77 do F" },
                    { input: ["0"], expectedOutput: "0 do C = 32 do F" },
                    { input: ["100"], expectedOutput: "100 do C = 212 do F" },
                    { input: ["37"], expectedOutput: "37 do C = 98.6 do F" },
                    { input: ["20"], expectedOutput: "20 do C = 68 do F" },
                    { input: ["30"], expectedOutput: "30 do C = 86 do F" },
                    { input: ["15"], expectedOutput: "15 do C = 59 do F" },
                    { input: ["40"], expectedOutput: "40 do C = 104 do F" },
                    { input: ["10"], expectedOutput: "10 do C = 50 do F" },
                    { input: ["35"], expectedOutput: "35 do C = 95 do F" }
                ]
            },
            {
                id: 10,
                title: "In Dãy Số",
                difficulty: "Trung Bình",
                description: "Viết chương trình nhận vào 3 số nguyên, in chúng ra cách nhau bởi dấu gạch ngang.",
                objective: "Luyện tập tham số sep trong hàm print().",
                sampleTestcase: { input: ["10", "20", "30"], expectedOutput: "10-20-30" },
                testcases: [
                    { input: ["10", "20", "30"], expectedOutput: "10-20-30" },
                    { input: ["1", "2", "3"], expectedOutput: "1-2-3" },
                    { input: ["100", "200", "300"], expectedOutput: "100-200-300" },
                    { input: ["5", "15", "25"], expectedOutput: "5-15-25" },
                    { input: ["7", "14", "21"], expectedOutput: "7-14-21" },
                    { input: ["50", "75", "100"], expectedOutput: "50-75-100" },
                    { input: ["8", "16", "24"], expectedOutput: "8-16-24" },
                    { input: ["12", "24", "36"], expectedOutput: "12-24-36" },
                    { input: ["9", "18", "27"], expectedOutput: "9-18-27" },
                    { input: ["6", "12", "18"], expectedOutput: "6-12-18" }
                ]
            },
            {
                id: 11,
                title: "Đổi Giây Ra Giờ:Phút:Giây",
                difficulty: "Khó",
                description: "Viết chương trình nhận vào một tổng số giây (nguyên), đổi nó ra thành dạng Giờ:Phút:Giây và in ra màn hình.",
                objective: "Vận dụng // và % nhiều lần để giải quyết bài toán đa bước.",
                sampleTestcase: { input: ["3661"], expectedOutput: "3661 giay tuong ung voi: 1:1:1" },
                testcases: [
                    { input: ["3661"], expectedOutput: "3661 giay tuong ung voi: 1:1:1" },
                    { input: ["3600"], expectedOutput: "3600 giay tuong ung voi: 1:0:0" },
                    { input: ["7200"], expectedOutput: "7200 giay tuong ung voi: 2:0:0" },
                    { input: ["3665"], expectedOutput: "3665 giay tuong ung voi: 1:1:5" },
                    { input: ["60"], expectedOutput: "60 giay tuong ung voi: 0:1:0" },
                    { input: ["90"], expectedOutput: "90 giay tuong ung voi: 0:1:30" },
                    { input: ["7325"], expectedOutput: "7325 giay tuong ung voi: 2:2:5" },
                    { input: ["45"], expectedOutput: "45 giay tuong ung voi: 0:0:45" },
                    { input: ["10800"], expectedOutput: "10800 giay tuong ung voi: 3:0:0" },
                    { input: ["5555"], expectedOutput: "5555 giay tuong ung voi: 1:32:35" }
                ]
            },
            {
                id: 12,
                title: "Tính Chỉ Số BMI",
                difficulty: "Khó",
                description: "Viết lại chương trình tính chỉ số BMI. Chương trình nhận vào cân nặng (kg) và chiều cao (m), sau đó in ra kết quả. Công thức: BMI = Cân nặng / (Chiều cao * Chiều cao).",
                objective: "Vận dụng tổng hợp các kiến thức.",
                sampleTestcase: { input: ["68.5", "1.75"], expectedOutput: "Chi so BMI cua ban la: 22.367346938775512" },
                testcases: [
                    { input: ["68.5", "1.75"], expectedOutput: "Chi so BMI cua ban la: 22.367346938775512" },
                    { input: ["70", "1.8"], expectedOutput: "Chi so BMI cua ban la: 21.604938271604937" },
                    { input: ["60", "1.65"], expectedOutput: "Chi so BMI cua ban la: 22.038567493112947" },
                    { input: ["80", "1.75"], expectedOutput: "Chi so BMI cua ban la: 26.122448979591837" },
                    { input: ["55", "1.6"], expectedOutput: "Chi so BMI cua ban la: 21.484375" },
                    { input: ["75", "1.7"], expectedOutput: "Chi so BMI cua ban la: 25.95155709342561" },
                    { input: ["65", "1.68"], expectedOutput: "Chi so BMI cua ban la: 23.030045351473923" },
                    { input: ["90", "1.85"], expectedOutput: "Chi so BMI cua ban la: 26.296018735362997" },
                    { input: ["50", "1.55"], expectedOutput: "Chi so BMI cua ban la: 20.811654526534862" },
                    { input: ["72", "1.72"], expectedOutput: "Chi so BMI cua ban la: 24.337748344370862" }
                ]
            },
            {
                id: 13,
                title: "Tổng Các Chữ Số",
                difficulty: "Khó",
                description: "Viết chương trình nhận vào một số nguyên có hai chữ số, tính tổng của hai chữ số đó và in kết quả.",
                objective: "Rèn luyện tư duy logic để tách các chữ số bằng toán tử // và %.",
                sampleTestcase: { input: ["75"], expectedOutput: "Tong hai chu so la: 12" },
                testcases: [
                    { input: ["75"], expectedOutput: "Tong hai chu so la: 12" },
                    { input: ["23"], expectedOutput: "Tong hai chu so la: 5" },
                    { input: ["89"], expectedOutput: "Tong hai chu so la: 17" },
                    { input: ["45"], expectedOutput: "Tong hai chu so la: 9" },
                    { input: ["67"], expectedOutput: "Tong hai chu so la: 13" },
                    { input: ["12"], expectedOutput: "Tong hai chu so la: 3" },
                    { input: ["99"], expectedOutput: "Tong hai chu so la: 18" },
                    { input: ["34"], expectedOutput: "Tong hai chu so la: 7" },
                    { input: ["56"], expectedOutput: "Tong hai chu so la: 11" },
                    { input: ["78"], expectedOutput: "Tong hai chu so la: 15" }
                ]
            },
            {
                id: 14,
                title: "Tính Tiền Sau Khi Giảm Giá",
                difficulty: "Khó",
                description: "Viết chương trình nhận vào giá gốc của một sản phẩm và phần trăm giảm giá. Tính và in ra số tiền được giảm và giá cuối cùng phải trả.",
                objective: "Vận dụng tính toán với số thực và logic thực tế.",
                sampleTestcase: { input: ["500000", "15"], expectedOutput: "So tien duoc giam: 75000\nGia cuoi cung phai tra: 425000" },
                testcases: [
                    { input: ["500000", "15"], expectedOutput: "So tien duoc giam: 75000\nGia cuoi cung phai tra: 425000" },
                    { input: ["100000", "10"], expectedOutput: "So tien duoc giam: 10000\nGia cuoi cung phai tra: 90000" },
                    { input: ["200000", "20"], expectedOutput: "So tien duoc giam: 40000\nGia cuoi cung phai tra: 160000" },
                    { input: ["300000", "25"], expectedOutput: "So tien duoc giam: 75000\nGia cuoi cung phai tra: 225000" },
                    { input: ["150000", "5"], expectedOutput: "So tien duoc giam: 7500\nGia cuoi cung phai tra: 142500" },
                    { input: ["80000", "30"], expectedOutput: "So tien duoc giam: 24000\nGia cuoi cung phai tra: 56000" },
                    { input: ["250000", "12"], expectedOutput: "So tien duoc giam: 30000\nGia cuoi cung phai tra: 220000" },
                    { input: ["400000", "18"], expectedOutput: "So tien duoc giam: 72000\nGia cuoi cung phai tra: 328000" },
                    { input: ["120000", "8"], expectedOutput: "So tien duoc giam: 9600\nGia cuoi cung phai tra: 110400" },
                    { input: ["600000", "22"], expectedOutput: "So tien duoc giam: 132000\nGia cuoi cung phai tra: 468000" }
                ]
            }
        ];
    </script>

    <script>
        // Coding practice functionality
        let currentProblem = null;
        let userProgress = {};

        // Simplified progress management (localStorage for now)
        function loadUserProgress() {
            try {
                const saved = localStorage.getItem('coding-practice-lesson4');
                if (saved) {
                    userProgress = JSON.parse(saved);
                }
            } catch (error) {
                console.error('Error loading progress:', error);
                userProgress = {};
            }
        }

        function saveUserProgress(problemId, score, code) {
            try {
                const currentScore = userProgress[problemId]?.score || 0;
                if (score > currentScore) {
                    userProgress[problemId] = {
                        score: score,
                        code: code,
                        timestamp: new Date().toISOString()
                    };
                    localStorage.setItem('coding-practice-lesson4', JSON.stringify(userProgress));
                }
            } catch (error) {
                console.error('Error saving progress:', error);
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadUserProgress();
            renderProblems();
        });

        function renderProblems() {
            const grid = document.getElementById('problemsGrid');
            grid.innerHTML = '';

            codingProblems.forEach(problem => {
                const progress = userProgress[problem.id] || { score: 0 };
                const progressPercent = (progress.score / problem.testcases.length) * 100;

                const card = document.createElement('div');
                card.className = 'problem-card';
                card.onclick = () => openProblem(problem);

                const difficultyClass = problem.difficulty === 'Dễ' ? 'easy' : 
                                      problem.difficulty === 'Trung Bình' ? 'medium' : 'hard';

                card.innerHTML = `
                    <div class="problem-header">
                        <div class="problem-title">${problem.id}. ${problem.title}</div>
                        <div class="difficulty ${difficultyClass}">${problem.difficulty}</div>
                    </div>
                    <div class="problem-description">${problem.description}</div>
                    <div class="problem-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progressPercent}%"></div>
                        </div>
                        <div class="progress-text">${progress.score}/${problem.testcases.length}</div>
                    </div>
                `;

                grid.appendChild(card);
            });
        }

        function openProblem(problem) {
            currentProblem = problem;
            const modal = document.getElementById('codingModal');
            
            document.getElementById('modalTitle').textContent = `${problem.id}. ${problem.title}`;
            document.getElementById('modalDescription').innerHTML = `<strong>Mô tả:</strong> ${problem.description}`;
            document.getElementById('modalObjective').innerHTML = `<strong>Mục tiêu:</strong> ${problem.objective}`;

            // Add sample testcase if available
            if (problem.sampleTestcase) {
                const sampleHtml = `
                    <div style="margin-top: 15px; padding: 10px; background: rgba(255, 255, 255, 0.1); border-radius: 5px;">
                        <strong>Ví dụ:</strong><br>
                        <strong>Input:</strong> ${problem.sampleTestcase.input.join(', ')}<br>
                        <strong>Output:</strong> ${problem.sampleTestcase.expectedOutput}
                    </div>
                `;
                document.getElementById('modalObjective').innerHTML += sampleHtml;
            }
            
            // Load saved code if exists
            const savedCode = userProgress[problem.id]?.code || '';
            document.getElementById('codeEditor').value = savedCode;
            
            // Reset results
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('testProgressFill').style.width = '0%';
            document.getElementById('testProgressText').textContent = 'Sẵn sàng chạy testcase...';
            
            modal.style.display = 'block';
        }

        // Modal event listeners
        document.querySelector('.close').onclick = function() {
            document.getElementById('codingModal').style.display = 'none';
        }

        window.onclick = function(event) {
            const modal = document.getElementById('codingModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        document.getElementById('resetCodeBtn').onclick = function() {
            document.getElementById('codeEditor').value = '';
        }

        document.getElementById('runTestsBtn').onclick = function() {
            if (currentProblem) {
                runTestcases();
            }
        }

        // Python code runner simulation
        function runTestcases() {
            const code = document.getElementById('codeEditor').value.trim();
            if (!code) {
                alert('Vui lòng nhập code trước khi chạy testcase!');
                return;
            }

            const resultsSection = document.getElementById('resultsSection');
            const progressFill = document.getElementById('testProgressFill');
            const progressText = document.getElementById('testProgressText');
            const testResults = document.getElementById('testResults');

            resultsSection.style.display = 'block';
            testResults.innerHTML = '';
            progressFill.style.width = '0%';

            let passedTests = 0;
            const totalTests = currentProblem.testcases.length;

            // Run tests with animation
            currentProblem.testcases.forEach((testcase, index) => {
                setTimeout(() => {
                    const result = runSingleTest(code, testcase, index + 1);
                    if (result.passed) passedTests++;

                    // Update progress
                    const progress = ((index + 1) / totalTests) * 100;
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `Đã chạy ${index + 1}/${totalTests} testcase - ${passedTests} passed`;

                    // Add test result
                    const testDiv = document.createElement('div');
                    testDiv.className = `test-case ${result.passed ? 'passed' : 'failed'}`;
                    testDiv.innerHTML = `
                        <strong>Test ${index + 1}:</strong> ${result.passed ? '✅ PASS' : '❌ FAIL'}<br>
                        <small>Expected: ${result.expected}<br>Got: ${result.actual}</small>
                    `;
                    testResults.appendChild(testDiv);

                    // Save progress when all tests complete
                    if (index === totalTests - 1) {
                        saveUserProgress(currentProblem.id, passedTests, code);
                        setTimeout(() => {
                            renderProblems(); // Update the main grid
                        }, 500);
                    }
                }, index * 200); // Stagger the tests for animation effect
            });
        }

        function runSingleTest(code, testcase, testNumber) {
            try {
                // Simple Python code simulation
                let output = simulatePythonCode(code, testcase.input);
                const expected = testcase.expectedOutput;

                // Normalize output for comparison
                output = output.trim();
                const normalizedExpected = expected.trim();

                return {
                    passed: output === normalizedExpected,
                    expected: normalizedExpected,
                    actual: output
                };
            } catch (error) {
                return {
                    passed: false,
                    expected: testcase.expectedOutput,
                    actual: `Error: ${error.message}`
                };
            }
        }

        function simulatePythonCode(code, inputs) {
            // This is a simplified Python interpreter simulation
            let inputIndex = 0;
            let output = [];

            // Replace input() calls with actual values
            let modifiedCode = code.replace(/input\s*\(\s*[^)]*\)/g, (match) => {
                if (inputIndex < inputs.length) {
                    return `"${inputs[inputIndex++]}"`;
                }
                return '""';
            });

            // Handle print statements with sep parameter
            modifiedCode = modifiedCode.replace(/print\s*\(([^)]+),\s*sep\s*=\s*["']([^"']*)["']\s*\)/g,
                (match, args, sep) => {
                    const argList = args.split(',').map(arg => arg.trim());
                    return `__print_sep__(${argList.join(', ')}, "${sep}")`;
                });

            // Handle print statements with end parameter
            modifiedCode = modifiedCode.replace(/print\s*\(([^)]+),\s*end\s*=\s*["']([^"']*)["']\s*\)/g,
                (match, args, end) => {
                    const argList = args.split(',').map(arg => arg.trim());
                    return `__print_end__(${argList.join(', ')}, "${end}")`;
                });

            // Handle f-strings
            modifiedCode = modifiedCode.replace(/f["']([^"']*\{[^}]*\}[^"']*)["']/g, (match, content) => {
                // Replace {variable} with ${variable} for template literals
                const templateContent = content.replace(/\{([^}]+)\}/g, '${$1}');
                return '`' + templateContent + '`';
            });

            // Handle regular print statements
            modifiedCode = modifiedCode.replace(/print\s*\(/g, '__print__(');

            // Handle basic Python operations
            modifiedCode = modifiedCode.replace(/(\w+)\s*\/\/\s*(\w+)/g, 'Math.floor($1 / $2)');
            modifiedCode = modifiedCode.replace(/(\w+)\s*\*\*\s*(\w+)/g, 'Math.pow($1, $2)');
            modifiedCode = modifiedCode.replace(/(\d+)\s*\*\*\s*(\d+)/g, 'Math.pow($1, $2)');
            modifiedCode = modifiedCode.replace(/int\s*\(/g, 'parseInt(');
            modifiedCode = modifiedCode.replace(/float\s*\(/g, 'parseFloat(');
            modifiedCode = modifiedCode.replace(/str\s*\(/g, 'String(');

            // Create execution context with helper functions
            const context = {
                __print__: (...args) => {
                    const result = args.map(arg => {
                        // Format numbers to remove unnecessary .0
                        if (typeof arg === 'number') {
                            return arg % 1 === 0 ? arg.toString() : arg.toString();
                        }
                        return String(arg);
                    }).join(' ');
                    output.push(result);
                },
                __print_sep__: (...args) => {
                    const sep = args.pop(); // Last argument is separator
                    const result = args.map(arg => {
                        if (typeof arg === 'number') {
                            return arg % 1 === 0 ? arg.toString() : arg.toString();
                        }
                        return String(arg);
                    }).join(sep);
                    output.push(result);
                },
                __print_end__: (...args) => {
                    const end = args.pop(); // Last argument is end character
                    const result = args.map(arg => {
                        if (typeof arg === 'number') {
                            return arg % 1 === 0 ? arg.toString() : arg.toString();
                        }
                        return String(arg);
                    }).join(' ');
                    if (end === '') {
                        if (output.length > 0) {
                            output[output.length - 1] += result;
                        } else {
                            output.push(result);
                        }
                    } else {
                        output.push(result + end);
                    }
                },
                Math: Math,
                parseInt: parseInt,
                parseFloat: parseFloat,
                String: String
            };

            try {
                // Execute the modified code with context
                const func = new Function(...Object.keys(context), modifiedCode);
                func(...Object.values(context));
                return output.join('\n');
            } catch (error) {
                throw new Error(`Code execution failed: ${error.message}`);
            }
        }
    </script>
</body>
</html>
